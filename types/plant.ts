export interface Plant {
  id: string;
  scientificName: string;
  commonName: string;
  imageUrl: string;
  description: string;
  careInstructions: CareInstructions;
  tags: string[];
}

export interface CareInstructions {
  light: LightRequirement;
  water: WaterRequirement;
  temperature: TemperatureRange;
  humidity: HumidityLevel;
  soil: string;
  fertilizer: string;
  toxicity: ToxicityLevel;
}

export type LightRequirement = 'low' | 'medium' | 'high';
export type WaterRequirement = 'low' | 'medium' | 'high';
export type HumidityLevel = 'low' | 'medium' | 'high';
export type ToxicityLevel = 'none' | 'mild' | 'moderate' | 'severe';

export interface TemperatureRange {
  min: number;
  max: number;
  unit: 'C' | 'F';
}

export interface IdentificationResult {
  plant: Plant;
  confidence: number;
  timestamp: Date;
  imageUri: string;
  problemDescription?: string;
  diagnosis?: string;
  treatment?: string;
  identificationData?: any; // Store full API response data
  diagnosisData?: any; // Store diagnosis-specific data
  diagnosisId?: string; // Database ID for diagnosis tracking
}

export interface GardenPlant extends Plant {
  addedDate: Date;
  nickname?: string;
  notes?: string;
  location?: string; // Location where the plant was identified/taken
  locationInGarden?: string; // Location within the user's garden
  lastWatered?: Date;
  lastFertilized?: Date;
  healthStatus?: 'healthy' | 'sick' | 'recovering' | 'critical';
  isPublic?: boolean;
  diagnosis?: {
    id: string;
    diagnosedProblem?: string;
    severity?: string;
    immediateActions?: string[];
    longTermCare?: string[];
    prognosis?: string;
    createdAt: Date;
  };
}