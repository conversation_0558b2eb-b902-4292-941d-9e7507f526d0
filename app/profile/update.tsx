import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TextInput, TouchableOpacity, Alert, Image, Modal } from 'react-native';
import { Stack, router } from 'expo-router';
import { ArrowLeft, Camera, Check, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import { DatabaseService, UserProfile } from '@/services/database';
import { supabase } from '@/lib/supabase';
import { checkNetworkConnectivity, isNetworkError, getNetworkErrorMessage, retryWithBackoff } from '@/utils/network';
import { runSupabaseDiagnostics } from '@/utils/supabase-test';
import { ConfigDebug } from '@/components/debug/ConfigDebug';

export default function UpdateProfileScreen() {
  const { user, session, refreshSession, isSessionValid } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [location, setLocation] = useState('');
  const [website, setWebsite] = useState('');
  const [avatarUri, setAvatarUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savingStatus, setSavingStatus] = useState<string>('');
  const [showConfigDebug, setShowConfigDebug] = useState(false);

  // Session validation and refresh
  const ensureValidSession = async (): Promise<boolean> => {
    console.log('ensureValidSession called');
    console.log('user exists:', !!user);
    console.log('session exists:', !!session);
    console.log('session valid:', session ? isSessionValid() : 'no session');

    if (!user || !session) {
      console.log('Missing user or session');
      Alert.alert('Authentication Error', 'Please log in again to continue.');
      return false;
    }

    if (!isSessionValid()) {
      console.log('Session expired, attempting to refresh...');
      const refreshed = await refreshSession();

      if (!refreshed) {
        console.log('Session refresh failed');
        Alert.alert(
          'Session Expired',
          'Your session has expired. Please log out and log back in.',
          [
            { text: 'OK', onPress: () => router.replace('/profile') }
          ]
        );
        return false;
      }

      console.log('Session refreshed successfully');

      // Wait a moment for the session to be properly updated
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Double-check that we have a valid session
    const { data: { session: currentSession }, error } = await supabase.auth.getSession();
    if (error || !currentSession) {
      console.error('Failed to get current session:', error);
      console.log('Current session check failed:', {
        error: error?.message,
        hasSession: !!currentSession,
        sessionExpiry: currentSession?.expires_at
      });
      Alert.alert(
        'Authentication Error',
        'Unable to verify your session. Please log out and back in.',
        [{ text: 'OK', onPress: () => router.replace('/profile') }]
      );
      return false;
    }

    console.log('Session validation successful');
    return true;
  };

  // Diagnostic function for troubleshooting
  const runDiagnostics = async () => {
    if (!user) return;

    console.log('Running Supabase diagnostics...');
    const diagnostics = await runSupabaseDiagnostics(user.id);

    console.log('Diagnostics results:', diagnostics);

    let message = 'Diagnostic Results:\n\n';
    message += `Connection Test: ${diagnostics.connectionTest.success ? 'PASSED' : 'FAILED'}\n`;

    if (!diagnostics.connectionTest.success) {
      message += `Connection Error: ${diagnostics.connectionTest.error}\n`;
    }

    if (diagnostics.profileTest) {
      message += `Profile Test: ${diagnostics.profileTest.success ? 'PASSED' : 'FAILED'}\n`;
      if (!diagnostics.profileTest.success) {
        message += `Profile Error: ${diagnostics.profileTest.error}\n`;
      }
    }

    if (diagnostics.recommendations.length > 0) {
      message += '\nRecommendations:\n';
      diagnostics.recommendations.forEach((rec, index) => {
        message += `${index + 1}. ${rec}\n`;
      });
    }

    Alert.alert('Diagnostics', message);
  };

  // Load user profile data
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Validate session first
        const sessionValid = await ensureValidSession();
        if (!sessionValid) {
          setIsLoading(false);
          return;
        }

        // Check network connectivity
        const hasNetwork = await checkNetworkConnectivity();
        if (!hasNetwork) {
          Alert.alert(
            'No Internet Connection',
            'Please check your internet connection and try again.',
            [
              { text: 'Retry', onPress: () => loadUserProfile() },
              { text: 'Go Back', onPress: () => router.replace('/profile') }
            ]
          );
          setIsLoading(false);
          return;
        }

        console.log('Attempting to load user profile for user ID:', user.id);
        const profile = await retryWithBackoff(async () => {
          return await DatabaseService.getUserProfile(user.id);
        });
        console.log('Profile loaded:', profile ? 'success' : 'not found', profile?.display_name);

        if (profile) {
          setUserProfile(profile);
          setDisplayName(profile.display_name || '');
          setBio(profile.bio || '');
          setLocation(profile.location || '');
          setWebsite(profile.website_url || '');
          setAvatarUri(profile.avatar_url);
        } else {
          // No profile found - create one with default values from user metadata
          console.log('No profile found, creating default profile...');
          const newProfile = {
            user_id: user.id,
            username: user.user_metadata?.preferred_username || null,
            display_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Plant Lover',
            bio: null,
            avatar_url: user.user_metadata?.avatar_url || null,
            location: null,
            website_url: null,
            is_public: false,
            allow_garden_sharing: false,
            allow_profile_indexing: false,
            experience_level: 'beginner' as const,
            total_identifications: 0,
            total_diagnoses: 0,
            community_points: 0,
            achievements: [],
          };

          const createdProfile = await DatabaseService.createUserProfile(newProfile);

          if (createdProfile) {
            setUserProfile(createdProfile);
            setDisplayName(createdProfile.display_name || '');
            setBio(createdProfile.bio || '');
            setLocation(createdProfile.location || '');
            setWebsite(createdProfile.website_url || '');
            setAvatarUri(createdProfile.avatar_url);
          } else {
            Alert.alert(
              'Profile Creation Failed',
              'Unable to create your profile. Please try again or contact support.',
              [
                { text: 'Go Back', onPress: () => router.replace('/profile') }
              ]
            );
          }
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
        const errorMessage = getNetworkErrorMessage(error);

        if (isNetworkError(error)) {
          Alert.alert(
            'Connection Error',
            errorMessage,
            [
              { text: 'Retry', onPress: () => loadUserProfile() },
              { text: 'Go Back', onPress: () => router.replace('/profile') }
            ]
          );
        } else {
          Alert.alert('Error', errorMessage, [
            { text: 'Retry', onPress: () => loadUserProfile() },
            { text: 'Diagnostics', onPress: runDiagnostics },
            ...__DEV__ ? [{ text: 'Debug Config', onPress: () => setShowConfigDebug(true) }] : [],
            { text: 'Go Back', onPress: () => router.replace('/profile') }
          ]);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, [user]);

  const pickImage = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setAvatarUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const uploadAvatar = async (uri: string): Promise<string | null> => {
    try {
      const fileExt = uri.split('.').pop();
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Convert URI to blob for upload - handle both web and mobile
      let blob: Blob;

      if (uri.startsWith('file://') || uri.startsWith('content://')) {
        // Mobile: Use fetch with proper error handling
        try {
          const response = await fetch(uri);
          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
          }
          blob = await response.blob();
        } catch (fetchError) {
          console.error('Error fetching image from URI:', fetchError);
          throw new Error('Failed to read image file. Please try selecting a different image.');
        }
      } else {
        // Web or other platforms
        const response = await fetch(uri);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        blob = await response.blob();
      }

      // Validate blob
      if (!blob || blob.size === 0) {
        throw new Error('Invalid image file selected');
      }

      // Check file size (limit to 5MB)
      if (blob.size > 5 * 1024 * 1024) {
        throw new Error('Image file is too large. Please select an image smaller than 5MB.');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(filePath, blob, {
          contentType: `image/${fileExt}`,
          upsert: true,
        });

      if (error) {
        console.error('Supabase upload error:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      // Re-throw with user-friendly message
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to upload avatar image');
    }
  };

  const handleSave = async () => {
    console.log('handleSave called');
    console.log('user:', !!user, 'userProfile:', !!userProfile);
    console.log('displayName:', displayName);

    if (!user) {
      console.log('Missing user, returning early');
      Alert.alert('Authentication Error', 'Please log in again to continue.');
      return;
    }

    // If userProfile is missing, try to load it first before creating
    if (!userProfile) {
      console.log('Missing userProfile, attempting to load existing profile first...');
      try {
        // First try to load existing profile
        const existingProfile = await DatabaseService.getUserProfile(user.id);
        if (existingProfile) {
          console.log('Found existing profile, using it');
          setUserProfile(existingProfile);
          // Continue with save using the existing profile
        } else {
          console.log('No existing profile found, creating new one...');
          const newProfile = {
            user_id: user.id,
            username: user.user_metadata?.preferred_username || null,
            display_name: displayName.trim() || user.user_metadata?.full_name || user.email?.split('@')[0] || 'Plant Lover',
            bio: bio.trim() || null,
            avatar_url: user.user_metadata?.avatar_url || null,
            location: location.trim() || null,
            website_url: website.trim() || null,
            is_public: false,
            allow_garden_sharing: false,
            allow_profile_indexing: false,
            experience_level: 'beginner' as const,
            total_identifications: 0,
            total_diagnoses: 0,
            community_points: 0,
            achievements: [],
          };

          const createdProfile = await DatabaseService.createUserProfile(newProfile);
          if (createdProfile) {
            setUserProfile(createdProfile);
            console.log('Profile created successfully during save');
          } else {
            Alert.alert('Error', 'Unable to create profile. Please try again.');
            return;
          }
        }
      } catch (error) {
        console.error('Error handling profile during save:', error);
        // If it's a duplicate key error, try to load the existing profile
        if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {
          console.log('Duplicate key error, trying to load existing profile...');
          try {
            const existingProfile = await DatabaseService.getUserProfile(user.id);
            if (existingProfile) {
              setUserProfile(existingProfile);
              console.log('Successfully loaded existing profile after duplicate error');
            } else {
              Alert.alert('Error', 'Profile exists but could not be loaded. Please refresh and try again.');
              return;
            }
          } catch (loadError) {
            console.error('Error loading existing profile after duplicate error:', loadError);
            Alert.alert('Error', 'Unable to access your profile. Please refresh and try again.');
            return;
          }
        } else {
          Alert.alert('Error', 'Unable to access your profile. Please try again.');
          return;
        }
      }
    }

    // Validate required fields
    if (!displayName.trim()) {
      Alert.alert('Validation Error', 'Display name is required');
      return;
    }

    // Validate session before proceeding
    console.log('Validating session...');
    const sessionValid = await ensureValidSession();
    if (!sessionValid) {
      console.log('Session validation failed');
      return;
    }

    // Double-check session is still valid right before save
    console.log('Final session check before save...');
    const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !currentSession) {
      console.error('Session check failed before save:', sessionError);
      Alert.alert('Session Error', 'Your session has expired. Please log out and back in.');
      return;
    }
    console.log('Session confirmed valid for save operation');

    console.log('Setting saving state...');
    setIsSaving(true);
    setSavingStatus('Preparing to save...');

    // Show immediate feedback that save is starting
    console.log('Starting profile save...');
    try {
      let avatarUrl = userProfile.avatar_url;

      // Upload new avatar if selected
      if (avatarUri && avatarUri !== userProfile.avatar_url) {
        setSavingStatus('Uploading profile picture...');
        try {
          const uploadedUrl = await uploadAvatar(avatarUri);
          if (uploadedUrl) {
            avatarUrl = uploadedUrl;
            setSavingStatus('Profile picture uploaded successfully');
          }
        } catch (uploadError) {
          console.error('Avatar upload failed:', uploadError);
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Unknown upload error';

          // Show error and ask user if they want to continue without avatar update
          Alert.alert(
            'Avatar Upload Failed',
            `${errorMessage}\n\nWould you like to save your other profile changes without updating the avatar?`,
            [
              { text: 'Cancel', style: 'cancel', onPress: () => setIsSaving(false) },
              { text: 'Save Without Avatar', onPress: () => continueWithProfileUpdate() }
            ]
          );
          return;
        }
      }

      setSavingStatus('Saving profile changes...');
      await continueWithProfileUpdate(avatarUrl);
    } catch (error) {
      console.error('Error updating profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      Alert.alert('Error', `Failed to update profile: ${errorMessage}`);
    } finally {
      setIsSaving(false);
      setSavingStatus('');
    }
  };

  const continueWithProfileUpdate = async (avatarUrl?: string) => {
    console.log('continueWithProfileUpdate called with avatarUrl:', avatarUrl);
    console.log('Current userProfile state:', !!userProfile);

    // Ensure we have a userProfile at this point
    if (!userProfile) {
      console.error('No userProfile available for update');
      Alert.alert('Error', 'Profile data not available. Please refresh and try again.');
      return;
    }

    try {
      // Check network connectivity first
      console.log('Checking network connectivity...');
      const hasNetwork = await checkNetworkConnectivity();
      if (!hasNetwork) {
        Alert.alert(
          'No Internet Connection',
          'Please check your internet connection and try again.',
          [
            { text: 'Retry', onPress: () => continueWithProfileUpdate(avatarUrl) },
            { text: 'Cancel', onPress: () => setIsSaving(false) }
          ]
        );
        return;
      }

      // Update profile with retry logic for network errors
      const updateData = {
        display_name: displayName.trim(),
        bio: bio.trim() || null,
        location: location.trim() || null,
        website_url: website.trim() || null,
        avatar_url: avatarUrl || userProfile.avatar_url,
      };

      console.log('Updating profile with data:', updateData);

      const updatedProfile = await retryWithBackoff(async () => {
        return await DatabaseService.updateUserProfile(user.id, updateData);
      });

      console.log('Profile update result:', updatedProfile);

      if (updatedProfile) {
        // Update local state to reflect changes immediately
        setUserProfile(updatedProfile);

        // Show success feedback with details
        const changes = [];
        if (displayName !== userProfile.display_name) changes.push('display name');
        if (bio !== userProfile.bio) changes.push('bio');
        if (location !== userProfile.location) changes.push('location');
        if (website !== userProfile.website_url) changes.push('website');
        if (avatarUrl !== userProfile.avatar_url) changes.push('profile picture');

        const changesText = changes.length > 0
          ? `\n\nUpdated: ${changes.join(', ')}`
          : '';

        Alert.alert(
          '✅ Profile Updated!',
          `Your profile has been successfully updated.${changesText}`,
          [
            { text: 'OK', onPress: () => router.replace('/profile') }
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to update profile. Please check your connection and try again.');
      }
    } catch (error) {
      console.error('Error updating profile in database:', error);
      const errorMessage = getNetworkErrorMessage(error);

      if (isNetworkError(error)) {
        Alert.alert(
          'Connection Error',
          errorMessage,
          [
            { text: 'Retry', onPress: () => continueWithProfileUpdate(avatarUrl) },
            { text: 'Cancel', onPress: () => { setIsSaving(false); setSavingStatus(''); } }
          ]
        );
      } else {
        Alert.alert('Error', `Failed to update profile: ${errorMessage}`, [
          { text: 'OK', onPress: () => { setIsSaving(false); setSavingStatus(''); } },
          { text: 'Diagnostics', onPress: () => { setIsSaving(false); setSavingStatus(''); runDiagnostics(); } }
        ]);
      }
    } finally {
      setIsSaving(false);
      setSavingStatus('');
    }
  };

  const currentAvatarUri = avatarUri || userProfile?.avatar_url || user?.user_metadata?.avatar_url;

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'Update Profile',
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.replace('/profile')} style={styles.headerButton}>
              <ArrowLeft size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }} 
      />

      <View style={styles.content}>
        {/* Avatar Section */}
        <Card style={styles.avatarCard}>
          <Text style={styles.sectionTitle}>Profile Picture</Text>
          <View style={styles.avatarContainer}>
            <Image 
              source={{ 
                uri: currentAvatarUri || 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80'
              }} 
              style={styles.avatar} 
            />
            <TouchableOpacity onPress={pickImage} style={styles.cameraButton}>
              <Camera size={20} color={Colors.background} />
            </TouchableOpacity>
          </View>
          <Text style={styles.avatarHint}>Tap the camera icon to change your profile picture</Text>
        </Card>

        {/* Profile Information */}
        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Display Name *</Text>
            <TextInput
              style={styles.textInput}
              value={displayName}
              onChangeText={setDisplayName}
              placeholder="Enter your display name"
              maxLength={50}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Bio</Text>
            <TextInput
              style={[styles.textInput, styles.bioInput]}
              value={bio}
              onChangeText={setBio}
              placeholder="Tell us about yourself..."
              multiline
              numberOfLines={3}
              maxLength={200}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Location</Text>
            <TextInput
              style={styles.textInput}
              value={location}
              onChangeText={setLocation}
              placeholder="Your location"
              maxLength={100}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Website</Text>
            <TextInput
              style={styles.textInput}
              value={website}
              onChangeText={setWebsite}
              placeholder="https://your-website.com"
              keyboardType="url"
              autoCapitalize="none"
              maxLength={200}
            />
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => router.replace('/profile')}
            variant="secondary"
            style={styles.cancelButton}
            icon={<X size={20} color={Colors.textMuted} />}
          />
          <Button
            title={isSaving ? (savingStatus || 'Saving...') : 'Save Changes'}
            onPress={handleSave}
            loading={isSaving}
            disabled={isSaving}
            style={styles.saveButton}
            icon={<Check size={20} color={Colors.background} />}
          />
        </View>

        {/* Debug button - remove after testing */}
        {__DEV__ && (
          <Button
            title="Debug Save"
            onPress={() => {
              console.log('Debug button pressed');
              console.log('Current state:', {
                displayName,
                bio,
                location,
                website,
                isSaving,
                userProfile: !!userProfile,
                user: !!user
              });
              handleSave();
            }}
            variant="outline"
            style={{ marginTop: 10 }}
          />
        )}

        {/* Saving Status Indicator */}
        {isSaving && savingStatus && (
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>{savingStatus}</Text>
          </View>
        )}

        {/* Debug Section - Only show in development */}
        {__DEV__ && (
          <View style={styles.debugSection}>
            <TouchableOpacity
              onPress={() => setShowConfigDebug(true)}
              style={styles.debugButton}
            >
              <Text style={styles.debugButtonText}>🔧 Debug Config</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Debug Modal */}
      <Modal
        visible={showConfigDebug}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <ConfigDebug onClose={() => setShowConfigDebug(false)} />
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    padding: 20,
  },
  avatarCard: {
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  avatarHint: {
    fontSize: 12,
    color: Colors.textMuted,
    textAlign: 'center',
  },
  formCard: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  bioInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  statusContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusText: {
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  debugSection: {
    marginTop: 20,
    padding: 16,
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fbbf24',
  },
  debugButton: {
    padding: 12,
    backgroundColor: '#fbbf24',
    borderRadius: 6,
    alignItems: 'center',
  },
  debugButtonText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 14,
  },
});
