import React from 'react';
import { StyleSheet, ScrollView, Text, View } from 'react-native';
import { Stack } from 'expo-router';
import { Colors } from '@/constants/colors';

export default function TermsOfServiceScreen() {
  return (
    <ScrollView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'Terms of Service',
          headerStyle: {
            backgroundColor: Colors.background,
          },
          headerTitleStyle: {
            color: Colors.text,
            fontWeight: '600',
          },
          headerTintColor: Colors.primary,
        }} 
      />
      
      <View style={styles.content}>
        <Text style={styles.title}>Terms of Service</Text>
        <Text style={styles.lastUpdated}>Last updated: {new Date().toLocaleDateString()}</Text>
        
        <Text style={styles.sectionTitle}>1. Acceptance of Terms</Text>
        <Text style={styles.paragraph}>
          By downloading, installing, or using PlantConnects ("the App"), you agree to be bound by these Terms of Service ("Terms").
          If you do not agree to these Terms, do not use the App.
        </Text>

        <Text style={styles.sectionTitle}>2. Description of Service</Text>
        <Text style={styles.paragraph}>
          PlantConnects is a mobile application that provides plant identification services, care instructions, and gardening guidance.
          The App uses artificial intelligence and machine learning technologies to analyze plant images and provide identification results.
          PlantConnects connects your plant discoveries to a global community wiki where users can share knowledge and experiences.
        </Text>
        
        <Text style={styles.sectionTitle}>3. User Accounts</Text>
        <Text style={styles.paragraph}>
          To access certain features of the App, you may be required to create an account. You are responsible for maintaining the 
          confidentiality of your account credentials and for all activities that occur under your account.
        </Text>
        
        <Text style={styles.sectionTitle}>4. Acceptable Use</Text>
        <Text style={styles.paragraph}>
          You agree to use the App only for lawful purposes and in accordance with these Terms. You may not use the App to:
          {'\n'}• Upload inappropriate, offensive, or copyrighted content
          {'\n'}• Attempt to reverse engineer or hack the App
          {'\n'}• Violate any applicable laws or regulations
          {'\n'}• Interfere with the App's functionality or security
        </Text>
        
        <Text style={styles.sectionTitle}>5. Accuracy of Information</Text>
        <Text style={styles.paragraph}>
          While we strive to provide accurate plant identification and care information, the App's results are for informational 
          purposes only. We do not guarantee the accuracy, completeness, or reliability of any identification or care recommendations. 
          Always consult with qualified professionals for critical plant care decisions.
        </Text>
        
        <Text style={styles.sectionTitle}>6. Privacy</Text>
        <Text style={styles.paragraph}>
          Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the App, 
          to understand our practices regarding your personal information.
        </Text>
        
        <Text style={styles.sectionTitle}>7. Intellectual Property</Text>
        <Text style={styles.paragraph}>
          The App and its original content, features, and functionality are owned by PlantConnects and are protected by
          international copyright, trademark, patent, trade secret, and other intellectual property laws.
        </Text>

        <Text style={styles.sectionTitle}>8. User Content and Community Contributions</Text>
        <Text style={styles.paragraph}>
          By uploading, posting, or sharing content through PlantConnects (including plant images, identification results,
          care tips, and community contributions), you grant PlantConnects a worldwide, non-exclusive, royalty-free license
          to use, reproduce, modify, adapt, publish, translate, distribute, and display such content in connection with
          operating and providing the service. You retain ownership of your original content, but agree that PlantConnects
          may use your contributions to improve the community wiki and enhance the service for all users.
        </Text>

        <Text style={styles.sectionTitle}>9. Community Guidelines</Text>
        <Text style={styles.paragraph}>
          When participating in the PlantConnects community, you agree to:
          {'\n'}• Provide accurate and helpful information
          {'\n'}• Respect other users and their contributions
          {'\n'}• Not post spam, inappropriate, or harmful content
          {'\n'}• Respect intellectual property rights of others
          {'\n'}• Follow all applicable laws and regulations
        </Text>

        <Text style={styles.sectionTitle}>10. Limitation of Liability</Text>
        <Text style={styles.paragraph}>
          In no event shall PlantConnects be liable for any indirect, incidental, special, consequential, or punitive damages,
          including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your
          use of the App.
        </Text>
        
        <Text style={styles.sectionTitle}>11. Termination</Text>
        <Text style={styles.paragraph}>
          We may terminate or suspend your account and bar access to the App immediately, without prior notice or liability,
          under our sole discretion, for any reason whatsoever, including without limitation if you breach the Terms.
        </Text>

        <Text style={styles.sectionTitle}>12. Changes to Terms</Text>
        <Text style={styles.paragraph}>
          We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at
          least 30 days notice prior to any new terms taking effect.
        </Text>

        <Text style={styles.sectionTitle}>13. Contact Information</Text>
        <Text style={styles.paragraph}>
          If you have any questions about these Terms of Service, please contact us at:
          {'\n'}Email: <EMAIL>
        </Text>

        <Text style={styles.sectionTitle}>14. Governing Law</Text>
        <Text style={styles.paragraph}>
          These Terms shall be interpreted and governed by the laws of the jurisdiction in which PlantConnects operates,
          without regard to its conflict of law provisions.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    color: Colors.textMuted,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 20,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: Colors.textLight,
    marginBottom: 16,
  },
});
