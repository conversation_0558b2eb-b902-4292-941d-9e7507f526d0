import React from 'react';
import { StyleSheet, ScrollView, Text, View } from 'react-native';
import { Stack } from 'expo-router';
import { Colors } from '@/constants/colors';

export default function PrivacyPolicyScreen() {
  return (
    <ScrollView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'Privacy Policy',
          headerStyle: {
            backgroundColor: Colors.background,
          },
          headerTitleStyle: {
            color: Colors.text,
            fontWeight: '600',
          },
          headerTintColor: Colors.primary,
        }} 
      />
      
      <View style={styles.content}>
        <Text style={styles.title}>Privacy Policy</Text>
        <Text style={styles.lastUpdated}>Last updated: {new Date().toLocaleDateString()}</Text>
        
        <Text style={styles.sectionTitle}>1. Introduction</Text>
        <Text style={styles.paragraph}>
          PlantConnects ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we
          collect, use, disclose, and safeguard your information when you use our mobile application and community platform.
        </Text>
        
        <Text style={styles.sectionTitle}>2. Information We Collect</Text>
        <Text style={styles.paragraph}>
          We may collect information about you in a variety of ways:
        </Text>
        
        <Text style={styles.subSectionTitle}>Personal Data</Text>
        <Text style={styles.paragraph}>
          • Email address (when you create an account)
          {'\n'}• Name or username
          {'\n'}• Profile information you choose to provide
          {'\n'}• Authentication data from third-party providers (Google)
        </Text>
        
        <Text style={styles.subSectionTitle}>Usage Data</Text>
        <Text style={styles.paragraph}>
          • Plant images you upload for identification
          {'\n'}• Community contributions and shared content
          {'\n'}• App usage patterns and preferences
          {'\n'}• Device information (model, operating system, unique device identifiers)
          {'\n'}• Log data (IP address, access times, pages viewed)
        </Text>

        <Text style={styles.sectionTitle}>3. How We Use Your Information</Text>
        <Text style={styles.paragraph}>
          We use the information we collect to:
          {'\n'}• Provide plant identification services
          {'\n'}• Facilitate community sharing and knowledge exchange
          {'\n'}• Maintain and improve our services and community platform
          {'\n'}• Create and manage your account
          {'\n'}• Send you technical notices and support messages
          {'\n'}• Respond to your comments and questions
          {'\n'}• Analyze usage patterns to improve user experience
          {'\n'}• Moderate community content and ensure platform safety
        </Text>
        
        <Text style={styles.sectionTitle}>4. Information Sharing</Text>
        <Text style={styles.paragraph}>
          We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except:
          {'\n'}• To trusted service providers who assist in operating our app
          {'\n'}• When required by law or to protect our rights
          {'\n'}• In connection with a business transfer or acquisition
        </Text>
        
        <Text style={styles.sectionTitle}>5. Data Security</Text>
        <Text style={styles.paragraph}>
          We implement appropriate security measures to protect your personal information against unauthorized access, 
          alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.
        </Text>
        
        <Text style={styles.sectionTitle}>6. Data Retention</Text>
        <Text style={styles.paragraph}>
          We retain your personal information only for as long as necessary to provide our services and fulfill the purposes 
          outlined in this Privacy Policy, unless a longer retention period is required by law.
        </Text>
        
        <Text style={styles.sectionTitle}>7. Your Rights</Text>
        <Text style={styles.paragraph}>
          Depending on your location, you may have the following rights:
          {'\n'}• Access to your personal data
          {'\n'}• Correction of inaccurate data
          {'\n'}• Deletion of your data
          {'\n'}• Data portability
          {'\n'}• Withdrawal of consent
        </Text>
        
        <Text style={styles.sectionTitle}>8. Third-Party Services</Text>
        <Text style={styles.paragraph}>
          Our app may contain links to third-party websites or integrate with third-party services (such as Google for authentication). 
          We are not responsible for the privacy practices of these third parties.
        </Text>
        
        <Text style={styles.sectionTitle}>9. Children's Privacy</Text>
        <Text style={styles.paragraph}>
          Our app is not intended for children under 13 years of age. We do not knowingly collect personal information from 
          children under 13. If we become aware that we have collected such information, we will take steps to delete it.
        </Text>
        
        <Text style={styles.sectionTitle}>10. International Data Transfers</Text>
        <Text style={styles.paragraph}>
          Your information may be transferred to and processed in countries other than your own. We ensure appropriate 
          safeguards are in place to protect your data during such transfers.
        </Text>
        
        <Text style={styles.sectionTitle}>11. Changes to This Privacy Policy</Text>
        <Text style={styles.paragraph}>
          We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new 
          Privacy Policy on this page and updating the "Last updated" date.
        </Text>
        
        <Text style={styles.sectionTitle}>12. Contact Us</Text>
        <Text style={styles.paragraph}>
          If you have any questions about this Privacy Policy, please contact us at:
          {'\n'}Email: <EMAIL>
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    color: Colors.textMuted,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 20,
    marginBottom: 12,
  },
  subSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 12,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: Colors.textLight,
    marginBottom: 16,
  },
});
