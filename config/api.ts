import Constants from 'expo-constants';

// Get OpenRouter API key from environment variables
const openrouterApiKey = Constants.expoConfig?.extra?.openrouterApiKey || process.env.OPENROUTER_API_KEY;

// Get OpenRouter model from environment variables with fallback
const openrouterModel = Constants.expoConfig?.extra?.openrouterModel || process.env.OPENROUTER_MODEL || 'google/gemini-2.5-flash-lite';

if (!openrouterApiKey) {
  console.warn('OpenRouter API key not found in environment variables');
}

// API Configuration
export const API_CONFIG = {
  OPENROUTER_API_KEY: openrouterApiKey,
  OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1',
  MODEL: openrouterModel,
};
