import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Heart, MessageCircle, Share2, User, Calendar, Leaf } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { DatabaseService, CommunityPost } from '@/services/database';

interface CommunityFeedProps {
  onPostPress?: (post: CommunityPost) => void;
}

export function CommunityFeed({ onPostPress }: CommunityFeedProps) {
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadPosts();
  }, []);

  const loadPosts = async () => {
    try {
      const publicPosts = await DatabaseService.getPublicGardenPosts(20, 0);
      setPosts(publicPosts);
    } catch (error) {
      console.error('Error loading community posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadPosts();
    setIsRefreshing(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  const renderPost = (post: CommunityPost) => {
    const gardenItem = post.garden_collections;
    const plantInfo = gardenItem?.plant_identifications;
    const userProfile = post.user_profiles;

    return (
      <Card key={post.id} style={styles.postCard}>
        <TouchableOpacity onPress={() => onPostPress?.(post)}>
          {/* User Header */}
          <View style={styles.postHeader}>
            <View style={styles.userInfo}>
              <View style={styles.avatarContainer}>
                {userProfile?.avatar_url ? (
                  <Image source={{ uri: userProfile.avatar_url }} style={styles.avatar} />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <User size={20} color={Colors.textMuted} />
                  </View>
                )}
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.username}>
                  {userProfile?.display_name || userProfile?.username || 'Plant Lover'}
                </Text>
                <View style={styles.postMeta}>
                  <Calendar size={12} color={Colors.textMuted} />
                  <Text style={styles.postDate}>{formatDate(post.created_at)}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Plant Image */}
          {plantInfo?.image_url && (
            <Image source={{ uri: plantInfo.image_url }} style={styles.plantImage} />
          )}

          {/* Post Content */}
          <View style={styles.postContent}>
            <View style={styles.plantHeader}>
              <Leaf size={16} color={Colors.primary} />
              <Text style={styles.plantName}>
                {gardenItem?.nickname || plantInfo?.common_name || 'Unknown Plant'}
              </Text>
            </View>
            
            {plantInfo?.scientific_name && (
              <Text style={styles.scientificName}>{plantInfo.scientific_name}</Text>
            )}

            {post.content && (
              <Text style={styles.postText}>{post.content}</Text>
            )}

            {gardenItem?.notes && (
              <Text style={styles.gardenNotes}>{gardenItem.notes}</Text>
            )}

            {/* Health Status */}
            {gardenItem?.health_status && (
              <View style={styles.healthStatus}>
                <View style={[
                  styles.healthIndicator,
                  { backgroundColor: getHealthColor(gardenItem.health_status) }
                ]} />
                <Text style={styles.healthText}>
                  {getHealthText(gardenItem.health_status)}
                </Text>
              </View>
            )}

            {/* Tags */}
            {post.tags && post.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {post.tags.slice(0, 3).map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
                {post.tags.length > 3 && (
                  <Text style={styles.moreTagsText}>+{post.tags.length - 3} more</Text>
                )}
              </View>
            )}
          </View>

          {/* Post Actions */}
          <View style={styles.postActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Heart size={18} color={Colors.textMuted} />
              <Text style={styles.actionText}>Like</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <MessageCircle size={18} color={Colors.textMuted} />
              <Text style={styles.actionText}>Comment</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <Share2 size={18} color={Colors.textMuted} />
              <Text style={styles.actionText}>Share</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return Colors.success;
      case 'recovering': return Colors.warning;
      case 'sick': return Colors.error;
      case 'critical': return '#DC3545';
      default: return Colors.textMuted;
    }
  };

  const getHealthText = (status: string) => {
    switch (status) {
      case 'healthy': return 'Healthy';
      case 'recovering': return 'Recovering';
      case 'sick': return 'Needs Attention';
      case 'critical': return 'Critical';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading community posts...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Community Garden</Text>
        <Text style={styles.subtitle}>
          Discover plants shared by the PlantConnects community
        </Text>
      </View>

      {posts.length === 0 ? (
        <Card style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            No community posts yet. Be the first to share your garden!
          </Text>
        </Card>
      ) : (
        <View style={styles.postsContainer}>
          {posts.map(renderPost)}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textMuted,
    lineHeight: 22,
  },
  postsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  postCard: {
    marginBottom: 16,
    padding: 0,
    overflow: 'hidden',
  },
  postHeader: {
    padding: 16,
    paddingBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  postMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postDate: {
    fontSize: 12,
    color: Colors.textMuted,
    marginLeft: 4,
  },
  plantImage: {
    width: '100%',
    height: 250,
    backgroundColor: Colors.secondary,
  },
  postContent: {
    padding: 16,
  },
  plantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  plantName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 6,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textMuted,
    marginBottom: 8,
  },
  postText: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 22,
    marginBottom: 8,
  },
  gardenNotes: {
    fontSize: 14,
    color: Colors.textMuted,
    lineHeight: 20,
    marginBottom: 12,
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  healthIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  healthText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textMuted,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
  },
  tag: {
    backgroundColor: Colors.secondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 12,
    color: Colors.textMuted,
    alignSelf: 'center',
  },
  postActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  actionText: {
    fontSize: 14,
    color: Colors.textMuted,
    marginLeft: 6,
  },
  emptyState: {
    margin: 20,
    padding: 24,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.textMuted,
    textAlign: 'center',
    lineHeight: 24,
  },
});
