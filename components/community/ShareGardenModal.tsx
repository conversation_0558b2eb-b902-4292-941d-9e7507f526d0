import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { X, Share2, Globe, Lock, Users, Eye } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { DatabaseService } from '@/services/database';
import { useAuth } from '@/hooks/useAuth';

interface ShareGardenModalProps {
  visible: boolean;
  gardenItemId: string;
  currentlyPublic: boolean;
  onClose: () => void;
  onShare: (isPublic: boolean, settings: ShareSettings) => void;
}

interface ShareSettings {
  isPublic: boolean;
  allowCommunityTips: boolean;
  showLocation: boolean;
  allowComments: boolean;
  seoTitle?: string;
  seoDescription?: string;
  tags: string[];
}

export function ShareGardenModal({ 
  visible, 
  gardenItemId, 
  currentlyPublic, 
  onClose, 
  onShare 
}: ShareGardenModalProps) {
  const [settings, setSettings] = useState<ShareSettings>({
    isPublic: currentlyPublic,
    allowCommunityTips: true,
    showLocation: false,
    allowComments: true,
    seoTitle: '',
    seoDescription: '',
    tags: [],
  });
  const [tagInput, setTagInput] = useState('');
  const { user } = useAuth();

  const handleShare = async () => {
    try {
      await DatabaseService.updateGardenCollection(gardenItemId, {
        is_public: settings.isPublic,
        allow_community_tips: settings.allowCommunityTips,
        seo_title: settings.seoTitle || undefined,
        seo_description: settings.seoDescription || undefined,
        tags: settings.tags.length > 0 ? settings.tags : undefined,
      });

      onShare(settings.isPublic, settings);
      onClose();
    } catch (error) {
      console.error('Error updating sharing settings:', error);
      Alert.alert('Error', 'Failed to update sharing settings. Please try again.');
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !settings.tags.includes(tagInput.trim())) {
      setSettings(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSettings(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Share with Community</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.visibilityCard}>
            <View style={styles.visibilityHeader}>
              <Share2 size={24} color={Colors.primary} />
              <Text style={styles.visibilityTitle}>Visibility Settings</Text>
            </View>
            
            <View style={styles.settingRow}>
              <View style={styles.settingLeft}>
                <Globe size={20} color={settings.isPublic ? Colors.success : Colors.textMuted} />
                <View style={styles.settingText}>
                  <Text style={styles.settingLabel}>Make Public</Text>
                  <Text style={styles.settingDesc}>
                    Share this plant with the PlantConnects community
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.isPublic}
                onValueChange={(value) => setSettings(prev => ({ ...prev, isPublic: value }))}
                trackColor={{ false: Colors.border, true: Colors.success }}
                thumbColor={settings.isPublic ? Colors.background : Colors.textMuted}
              />
            </View>
          </Card>

          {settings.isPublic && (
            <>
              <Card style={styles.communityCard}>
                <Text style={styles.sectionTitle}>Community Features</Text>
                
                <View style={styles.settingRow}>
                  <View style={styles.settingLeft}>
                    <Users size={20} color={Colors.primary} />
                    <View style={styles.settingText}>
                      <Text style={styles.settingLabel}>Allow Community Tips</Text>
                      <Text style={styles.settingDesc}>
                        Let other users share care advice
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.allowCommunityTips}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, allowCommunityTips: value }))}
                    trackColor={{ false: Colors.border, true: Colors.primary }}
                    thumbColor={settings.allowCommunityTips ? Colors.background : Colors.textMuted}
                  />
                </View>

                <View style={styles.settingRow}>
                  <View style={styles.settingLeft}>
                    <Eye size={20} color={Colors.primary} />
                    <View style={styles.settingText}>
                      <Text style={styles.settingLabel}>Allow Comments</Text>
                      <Text style={styles.settingDesc}>
                        Enable community discussions
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.allowComments}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, allowComments: value }))}
                    trackColor={{ false: Colors.border, true: Colors.primary }}
                    thumbColor={settings.allowComments ? Colors.background : Colors.textMuted}
                  />
                </View>
              </Card>

              <Card style={styles.seoCard}>
                <Text style={styles.sectionTitle}>SEO & Discovery</Text>
                <Text style={styles.seoDesc}>
                  Help others discover your plant on the PlantConnects website
                </Text>
                
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Custom Title (Optional)</Text>
                  <TextInput
                    style={styles.textInput}
                    value={settings.seoTitle}
                    onChangeText={(text) => setSettings(prev => ({ ...prev, seoTitle: text }))}
                    placeholder="My Beautiful Garden Rose"
                    placeholderTextColor={Colors.textMuted}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Description (Optional)</Text>
                  <TextInput
                    style={[styles.textInput, styles.textArea]}
                    value={settings.seoDescription}
                    onChangeText={(text) => setSettings(prev => ({ ...prev, seoDescription: text }))}
                    placeholder="Share details about your plant's journey, care tips, or interesting facts..."
                    placeholderTextColor={Colors.textMuted}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Tags</Text>
                  <View style={styles.tagInputContainer}>
                    <TextInput
                      style={[styles.textInput, styles.tagInput]}
                      value={tagInput}
                      onChangeText={setTagInput}
                      placeholder="Add a tag..."
                      placeholderTextColor={Colors.textMuted}
                      onSubmitEditing={addTag}
                    />
                    <Button
                      title="Add"
                      onPress={addTag}
                      style={styles.addTagButton}
                      disabled={!tagInput.trim()}
                    />
                  </View>
                  
                  {settings.tags.length > 0 && (
                    <View style={styles.tagsList}>
                      {settings.tags.map((tag, index) => (
                        <TouchableOpacity
                          key={index}
                          style={styles.tag}
                          onPress={() => removeTag(tag)}
                        >
                          <Text style={styles.tagText}>{tag}</Text>
                          <X size={14} color={Colors.background} />
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>
              </Card>
            </>
          )}

          {!settings.isPublic && (
            <Card style={styles.privateCard}>
              <View style={styles.privateHeader}>
                <Lock size={24} color={Colors.textMuted} />
                <Text style={styles.privateTitle}>Private Garden</Text>
              </View>
              <Text style={styles.privateDesc}>
                This plant will remain private and only visible to you. You can make it public anytime to share with the community.
              </Text>
            </Card>
          )}
        </ScrollView>

        <View style={styles.footer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={onClose}
            style={styles.cancelButton}
          />
          <Button
            title={settings.isPublic ? "Share Publicly" : "Keep Private"}
            onPress={handleShare}
            style={styles.shareButton}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  visibilityCard: {
    marginTop: 20,
  },
  visibilityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  visibilityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  settingDesc: {
    fontSize: 12,
    color: Colors.textMuted,
    lineHeight: 16,
  },
  communityCard: {
    marginTop: 16,
  },
  seoCard: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  seoDesc: {
    fontSize: 14,
    color: Colors.textMuted,
    marginBottom: 16,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.text,
    backgroundColor: Colors.background,
  },
  textArea: {
    minHeight: 80,
  },
  tagInputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  tagInput: {
    flex: 1,
  },
  addTagButton: {
    paddingHorizontal: 16,
  },
  tagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  tagText: {
    fontSize: 12,
    color: Colors.background,
    fontWeight: '500',
  },
  privateCard: {
    marginTop: 20,
    backgroundColor: Colors.secondary,
  },
  privateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  privateTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginLeft: 8,
  },
  privateDesc: {
    fontSize: 14,
    color: Colors.textMuted,
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  shareButton: {
    flex: 2,
  },
});
