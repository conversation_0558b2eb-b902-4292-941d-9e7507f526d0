import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { Camera, Plus, TrendingUp, Calendar, Star } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { DatabaseService, RecoveryTracking } from '@/services/database';
import { useAuth } from '@/hooks/useAuth';

interface RecoveryTrackerProps {
  diagnosisId: string;
  onImageCapture?: (uri: string) => void;
}

export function RecoveryTracker({ diagnosisId, onImageCapture }: RecoveryTrackerProps) {
  const [recoveryEntries, setRecoveryEntries] = useState<RecoveryTracking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newEntry, setNewEntry] = useState({
    progressNotes: '',
    treatmentsApplied: '',
    effectivenessRating: 3,
    sideEffects: '',
    nextSteps: '',
    recoveryStatus: 'in_progress' as const,
  });
  const { user } = useAuth();

  useEffect(() => {
    loadRecoveryEntries();
  }, [diagnosisId]);

  const loadRecoveryEntries = async () => {
    try {
      const entries = await DatabaseService.getRecoveryTracking(diagnosisId);
      setRecoveryEntries(entries);
    } catch (error) {
      console.error('Error loading recovery entries:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddEntry = async () => {
    if (!user) return;

    try {
      const entry = await DatabaseService.createRecoveryTracking({
        diagnosis_id: diagnosisId,
        user_id: user.id,
        progress_notes: newEntry.progressNotes,
        treatments_applied: newEntry.treatmentsApplied.split(',').map(t => t.trim()).filter(Boolean),
        effectiveness_rating: newEntry.effectivenessRating,
        side_effects: newEntry.sideEffects || undefined,
        next_steps: newEntry.nextSteps || undefined,
        recovery_status: newEntry.recoveryStatus,
        date_applied: new Date().toISOString().split('T')[0],
        is_public: false,
      });

      if (entry) {
        setRecoveryEntries(prev => [entry, ...prev]);
        setNewEntry({
          progressNotes: '',
          treatmentsApplied: '',
          effectivenessRating: 3,
          sideEffects: '',
          nextSteps: '',
          recoveryStatus: 'in_progress',
        });
        setShowAddForm(false);
      }
    } catch (error) {
      console.error('Error adding recovery entry:', error);
      Alert.alert('Error', 'Failed to save recovery entry. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'improving': return Colors.success;
      case 'recovered': return Colors.primary;
      case 'worsening': return Colors.error;
      case 'failed': return Colors.error;
      default: return Colors.warning;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_progress': return 'In Progress';
      case 'improving': return 'Improving';
      case 'recovered': return 'Recovered';
      case 'worsening': return 'Worsening';
      case 'failed': return 'Treatment Failed';
      default: return status;
    }
  };

  const renderStarRating = (rating: number, onPress?: (rating: number) => void) => {
    return (
      <View style={styles.starContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onPress?.(star)}
            disabled={!onPress}
          >
            <Star
              size={20}
              color={star <= rating ? Colors.warning : Colors.border}
              fill={star <= rating ? Colors.warning : 'transparent'}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading recovery tracking...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TrendingUp size={24} color={Colors.primary} />
          <Text style={styles.title}>Recovery Tracking</Text>
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddForm(!showAddForm)}
        >
          <Plus size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {showAddForm && (
        <Card style={styles.addForm}>
          <Text style={styles.formTitle}>Add Recovery Update</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Progress Notes</Text>
            <TextInput
              style={styles.textInput}
              value={newEntry.progressNotes}
              onChangeText={(text) => setNewEntry(prev => ({ ...prev, progressNotes: text }))}
              placeholder="Describe the current condition and any changes..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Treatments Applied</Text>
            <TextInput
              style={styles.textInput}
              value={newEntry.treatmentsApplied}
              onChangeText={(text) => setNewEntry(prev => ({ ...prev, treatmentsApplied: text }))}
              placeholder="List treatments applied (comma separated)"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Effectiveness Rating</Text>
            {renderStarRating(newEntry.effectivenessRating, (rating) => 
              setNewEntry(prev => ({ ...prev, effectivenessRating: rating }))
            )}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Recovery Status</Text>
            <View style={styles.statusButtons}>
              {['in_progress', 'improving', 'recovered', 'worsening'].map((status) => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.statusButton,
                    newEntry.recoveryStatus === status && styles.statusButtonActive
                  ]}
                  onPress={() => setNewEntry(prev => ({ ...prev, recoveryStatus: status as any }))}
                >
                  <Text style={[
                    styles.statusButtonText,
                    newEntry.recoveryStatus === status && styles.statusButtonTextActive
                  ]}>
                    {getStatusText(status)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formActions}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => setShowAddForm(false)}
              style={styles.cancelButton}
            />
            <Button
              title="Save Entry"
              onPress={handleAddEntry}
              style={styles.saveButton}
            />
          </View>
        </Card>
      )}

      <ScrollView style={styles.entriesList} showsVerticalScrollIndicator={false}>
        {recoveryEntries.length === 0 ? (
          <Card style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No recovery entries yet. Add your first update to start tracking progress.
            </Text>
          </Card>
        ) : (
          recoveryEntries.map((entry) => (
            <Card key={entry.id} style={styles.entryCard}>
              <View style={styles.entryHeader}>
                <View style={styles.entryDate}>
                  <Calendar size={16} color={Colors.textMuted} />
                  <Text style={styles.entryDateText}>
                    {new Date(entry.date_applied).toLocaleDateString()}
                  </Text>
                </View>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(entry.recovery_status) }]}>
                  <Text style={styles.statusBadgeText}>
                    {getStatusText(entry.recovery_status)}
                  </Text>
                </View>
              </View>

              {entry.progress_notes && (
                <Text style={styles.entryNotes}>{entry.progress_notes}</Text>
              )}

              {entry.treatments_applied && entry.treatments_applied.length > 0 && (
                <View style={styles.treatmentsList}>
                  <Text style={styles.treatmentsTitle}>Treatments Applied:</Text>
                  {entry.treatments_applied.map((treatment, index) => (
                    <Text key={index} style={styles.treatmentItem}>• {treatment}</Text>
                  ))}
                </View>
              )}

              {entry.effectiveness_rating && (
                <View style={styles.ratingContainer}>
                  <Text style={styles.ratingLabel}>Effectiveness:</Text>
                  {renderStarRating(entry.effectiveness_rating)}
                </View>
              )}

              {entry.progress_image_url && (
                <Image source={{ uri: entry.progress_image_url }} style={styles.progressImage} />
              )}
            </Card>
          ))
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  addButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.secondary,
  },
  addForm: {
    marginBottom: 16,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: Colors.text,
    backgroundColor: Colors.background,
  },
  starContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background,
  },
  statusButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  statusButtonText: {
    fontSize: 12,
    color: Colors.text,
  },
  statusButtonTextActive: {
    color: Colors.background,
  },
  formActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  entriesList: {
    flex: 1,
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.textMuted,
    textAlign: 'center',
    lineHeight: 24,
  },
  entryCard: {
    marginBottom: 12,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entryDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryDateText: {
    fontSize: 14,
    color: Colors.textMuted,
    marginLeft: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.background,
  },
  entryNotes: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 22,
    marginBottom: 12,
  },
  treatmentsList: {
    marginBottom: 12,
  },
  treatmentsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  treatmentItem: {
    fontSize: 14,
    color: Colors.textMuted,
    marginLeft: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginRight: 8,
  },
  progressImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginTop: 8,
  },
});
