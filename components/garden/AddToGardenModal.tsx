import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  Modal,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { X, Leaf, FileText } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Plant } from '@/types/plant';

interface AddToGardenModalProps {
  visible: boolean;
  plant: Plant | null;
  onClose: () => void;
  onAdd: (plant: Plant, nickname?: string, notes?: string) => void;
  onAddAndShare: (plant: Plant, nickname?: string, notes?: string) => void;
  onShareOnly: (plant: Plant) => void;
}

export function AddToGardenModal({ visible, plant, onClose, onAdd, onAddAndShare, onShareOnly }: AddToGardenModalProps) {
  const [nickname, setNickname] = useState('');
  const [notes, setNotes] = useState('');

  const handleAddAndShare = () => {
    if (plant) {
      onAddAndShare(plant, nickname || plant.commonName, notes);
      handleClose();
    }
  };

  const handleShareOnly = () => {
    if (plant) {
      onShareOnly(plant);
      handleClose();
    }
  };

  const handleCancel = () => {
    handleClose();
  };

  const handleClose = () => {
    setNickname('');
    setNotes('');
    onClose();
  };

  if (!plant) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Add to My Garden</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <X size={24} color={Colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.plantCard}>
            <View style={styles.plantInfo}>
              <View style={styles.iconContainer}>
                <Leaf size={24} color={Colors.primary} />
              </View>
              <View style={styles.plantDetails}>
                <Text style={styles.plantName}>{plant.commonName}</Text>
                <Text style={styles.scientificName}>{plant.scientificName}</Text>
              </View>
            </View>
          </Card>

          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Plant Details</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nickname (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={nickname}
                onChangeText={setNickname}
                placeholder={plant.commonName}
                placeholderTextColor={Colors.textMuted}
              />
              <Text style={styles.inputHint}>Give your plant a personal name</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Notes</Text>
              <View style={styles.notesContainer}>
                <View style={styles.notesHeader}>
                  <FileText size={16} color={Colors.textMuted} />
                  <Text style={styles.notesHeaderText}>Personal Notes</Text>
                </View>
                <TextInput
                  style={styles.notesInput}
                  value={notes}
                  onChangeText={setNotes}
                  placeholder="Add notes about your plant's location, care schedule, observations, or any other details..."
                  placeholderTextColor={Colors.textMuted}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              <Text style={styles.inputHint}>
                Track your plant's progress, care schedule, and observations
              </Text>
            </View>


          </View>
        </ScrollView>

        <View style={styles.footer}>
          {/* First row - Main action button */}
          <Button
            title="Add to my Garden and Share with the Community"
            onPress={handleAddAndShare}
            style={styles.primaryButton}
            textStyle={styles.primaryButtonText}
          />

          {/* Second row - Secondary actions */}
          <View style={styles.secondaryRow}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleCancel}
              style={styles.cancelButton}
            />
            <Button
              title="Share with Community"
              onPress={handleShareOnly}
              style={styles.shareButton}
              textStyle={styles.shareButtonText}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  plantCard: {
    marginTop: 20,
    marginBottom: 24,
  },
  plantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  plantDetails: {
    flex: 1,
  },
  plantName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textMuted,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text,
    backgroundColor: Colors.background,
  },
  inputHint: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
  notesContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 12,
    backgroundColor: Colors.background,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  notesHeaderText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textMuted,
    marginLeft: 8,
  },
  notesInput: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text,
    minHeight: 100,
  },

  footer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    gap: 16,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.white,
    textAlign: 'center',
  },
  secondaryRow: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    borderColor: Colors.textLight,
  },
  shareButton: {
    flex: 1,
    backgroundColor: Colors.accent1,
    borderColor: Colors.accent1,
  },
  shareButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
});
