import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors } from '@/constants/colors';
import { GardenPlant } from '@/types/plant';
import { Card } from '@/components/ui/Card';
import { Sun, Droplets, Thermometer, Wind } from 'lucide-react-native';

interface DetailedGardenPlantCardProps {
  plant: GardenPlant;
  onPress: () => void;
}

export function DetailedGardenPlantCard({ plant, onPress }: DetailedGardenPlantCardProps) {
  const getHealthStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'healthy': return Colors.success;
      case 'warning': return Colors.accent1;
      case 'critical': return Colors.error;
      default: return Colors.textLight;
    }
  };

  const truncateNotes = (notes?: string, maxLength: number = 80) => {
    if (!notes) return '';
    return notes.length > maxLength ? `${notes.substring(0, maxLength)}...` : notes;
  };

  const renderCareInfo = () => {
    if (!plant.careInstructions) return null;

    return (
      <View style={styles.careContainer}>
        <Text style={styles.careTitle}>Care Requirements</Text>
        <View style={styles.careGrid}>
          <View style={styles.careItem}>
            <Sun size={16} color={Colors.primary} />
            <Text style={styles.careLabel}>Light</Text>
            <Text style={styles.careValue}>{plant.careInstructions.light}</Text>
          </View>
          <View style={styles.careItem}>
            <Droplets size={16} color={Colors.primary} />
            <Text style={styles.careLabel}>Water</Text>
            <Text style={styles.careValue}>{plant.careInstructions.water}</Text>
          </View>
          <View style={styles.careItem}>
            <Thermometer size={16} color={Colors.primary} />
            <Text style={styles.careLabel}>Temp</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions?.temperature?.min || 18}°-{plant.careInstructions?.temperature?.max || 25}°{plant.careInstructions?.temperature?.unit || 'C'}
            </Text>
          </View>
          <View style={styles.careItem}>
            <Wind size={16} color={Colors.primary} />
            <Text style={styles.careLabel}>Humidity</Text>
            <Text style={styles.careValue}>{plant.careInstructions.humidity}</Text>
          </View>
        </View>
        
        {plant.careInstructions.soil && (
          <View style={styles.soilContainer}>
            <Text style={styles.soilLabel}>Soil:</Text>
            <Text style={styles.soilValue}>{plant.careInstructions.soil}</Text>
          </View>
        )}
        
        {plant.careInstructions.fertilizer && (
          <View style={styles.fertilizerContainer}>
            <Text style={styles.fertilizerLabel}>Fertilizer:</Text>
            <Text style={styles.fertilizerValue}>{plant.careInstructions.fertilizer}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderDiagnosisInfo = () => {
    if (!plant.diagnosis) return null;

    return (
      <View style={styles.diagnosisContainer}>
        <Text style={styles.diagnosisTitle}>Diagnosis Information</Text>
        <View style={styles.diagnosisItem}>
          <Text style={styles.diagnosisLabel}>Problem:</Text>
          <Text style={styles.diagnosisValue}>{plant.diagnosis.diagnosedProblem}</Text>
        </View>
        <View style={styles.diagnosisItem}>
          <Text style={styles.diagnosisLabel}>Severity:</Text>
          <Text style={[styles.diagnosisValue, { 
            color: plant.diagnosis.severity === 'high' ? Colors.error : 
                   plant.diagnosis.severity === 'medium' ? Colors.accent1 : Colors.success 
          }]}>
            {plant.diagnosis.severity}
          </Text>
        </View>
        {plant.diagnosis.likelyCauses && plant.diagnosis.likelyCauses.length > 0 && (
          <View style={styles.diagnosisItem}>
            <Text style={styles.diagnosisLabel}>Likely Causes:</Text>
            <Text style={styles.diagnosisValue}>{plant.diagnosis.likelyCauses.join(', ')}</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <Card style={styles.card}>
        <View style={styles.header}>
          <View style={styles.plantInfo}>
            <Text style={styles.commonName}>{plant.commonName}</Text>
            <Text style={styles.scientificName}>{plant.scientificName}</Text>
            {plant.nickname && (
              <Text style={styles.nickname}>"{plant.nickname}"</Text>
            )}
          </View>
          <View style={styles.statusContainer}>
            {plant.healthStatus && (
              <View style={[styles.healthBadge, { backgroundColor: getHealthStatusColor(plant.healthStatus) }]}>
                <Text style={styles.healthText}>{plant.healthStatus}</Text>
              </View>
            )}
          </View>
        </View>

        {plant.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.notesLabel}>Notes:</Text>
            <Text style={styles.notesText}>{truncateNotes(plant.notes)}</Text>
          </View>
        )}

        {plant.diagnosis ? renderDiagnosisInfo() : renderCareInfo()}
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  card: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  plantInfo: {
    flex: 1,
  },
  commonName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 2,
  },
  nickname: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  healthBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  healthText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.white,
    textTransform: 'capitalize',
  },
  notesContainer: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: Colors.backgroundLight,
    borderRadius: 8,
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: Colors.textLight,
    lineHeight: 20,
  },
  careContainer: {
    marginTop: 8,
  },
  careTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  careGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  careItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 12,
    color: Colors.textLight,
    marginLeft: 6,
    flex: 1,
  },
  careValue: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text,
    textAlign: 'right',
  },
  soilContainer: {
    marginBottom: 8,
  },
  soilLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  soilValue: {
    fontSize: 12,
    color: Colors.textLight,
    lineHeight: 16,
  },
  fertilizerContainer: {
    marginBottom: 8,
  },
  fertilizerLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  fertilizerValue: {
    fontSize: 12,
    color: Colors.textLight,
    lineHeight: 16,
  },
  diagnosisContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent1,
  },
  diagnosisTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  diagnosisItem: {
    flexDirection: 'row',
    marginBottom: 6,
  },
  diagnosisLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    width: 80,
  },
  diagnosisValue: {
    fontSize: 12,
    color: Colors.textLight,
    flex: 1,
    textTransform: 'capitalize',
  },
});
