import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Modal,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Switch,
} from 'react-native';
import { X, Edit3, Trash2, Share2, Calendar, Leaf, Eye, EyeOff, XCircle, Check } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { GardenPlant } from '@/types/plant';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface PlantDetailModalProps {
  visible: boolean;
  plant: GardenPlant | null;
  onClose: () => void;
  onUpdate: (plantId: string, updates: { nickname?: string; notes?: string; locationInGarden?: string; healthStatus?: string; isPublic?: boolean }, sourceTab?: 'identified' | 'diagnosed') => void;
  onRemove: (plantId: string) => void;
  sourceTab?: 'identified' | 'diagnosed';
}

export const PlantDetailModal: React.FC<PlantDetailModalProps> = ({
  visible,
  plant,
  onClose,
  onUpdate,
  onRemove,
  sourceTab,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedNickname, setEditedNickname] = useState('');
  const [editedNotes, setEditedNotes] = useState('');
  const [editedLocation, setEditedLocation] = useState('');
  const [editedHealthStatus, setEditedHealthStatus] = useState<'healthy' | 'sick' | 'recovering' | 'critical'>('healthy');
  const [editedIsPublic, setEditedIsPublic] = useState(false);

  React.useEffect(() => {
    if (plant) {
      setEditedNickname(plant.nickname || '');
      setEditedNotes(plant.notes || '');
      setEditedLocation(plant.locationInGarden || '');
      setEditedHealthStatus(plant.healthStatus || 'healthy');
      setEditedIsPublic(plant.isPublic || false);
    }
  }, [plant]);

  const handleSave = () => {
    if (!plant) return;

    onUpdate(plant.id, {
      nickname: editedNickname.trim() || undefined,
      notes: editedNotes.trim() || undefined,
      locationInGarden: editedLocation.trim() || undefined,
      healthStatus: editedHealthStatus,
      isPublic: editedIsPublic,
    }, sourceTab);
    setIsEditing(false);
  };

  const handleRemove = () => {
    if (!plant) {
      console.log('Plant detail modal: No plant selected for removal');
      return;
    }

    console.log('Plant detail modal: Calling onRemove directly for plant:', plant.id);
    onRemove(plant.id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return Colors.success;
      case 'sick':
        return Colors.error;
      case 'recovering':
        return Colors.warning;
      case 'critical':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'Healthy';
      case 'sick':
        return 'Sick';
      case 'recovering':
        return 'Recovering';
      case 'critical':
        return 'Critical';
      default:
        return 'Unknown';
    }
  };

  const getHealthStatusColor = (status: 'healthy' | 'sick' | 'recovering' | 'critical') => {
    switch (status) {
      case 'healthy': return Colors.success;
      case 'sick': return Colors.warning;
      case 'recovering': return Colors.primary;
      case 'critical': return Colors.error;
      default: return Colors.success;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!plant) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Plant Details</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Plant Image and Basic Info */}
          <Card style={styles.plantCard}>
            <Image source={{ uri: plant.imageUrl }} style={styles.plantImage} resizeMode="cover" />
            <View style={styles.plantInfo}>
              <View style={styles.plantHeader}>
                <View style={styles.plantNames}>
                  <Text style={styles.commonName}>
                    {plant.nickname || plant.commonName}
                  </Text>
                  <Text style={styles.scientificName}>{plant.scientificName}</Text>
                </View>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(plant.healthStatus || 'healthy') }]}>
                  <Text style={styles.statusText}>
                    {getStatusText(plant.healthStatus || 'healthy')}
                  </Text>
                </View>
              </View>

              <View style={styles.dateContainer}>
                <Calendar size={16} color={Colors.textMuted} />
                <Text style={styles.dateText}>
                  Added {formatDate(plant.addedDate)}
                </Text>
              </View>
            </View>
          </Card>

          {/* Editable Details */}
          <Card style={styles.detailsCard}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Plant Details</Text>
            </View>

            {isEditing ? (
              <View style={styles.editForm}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Nickname</Text>
                  <TextInput
                    style={styles.textInput}
                    value={editedNickname}
                    onChangeText={setEditedNickname}
                    placeholder={plant.commonName}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Notes</Text>
                  <TextInput
                    style={[styles.textInput, styles.notesInput]}
                    value={editedNotes}
                    onChangeText={setEditedNotes}
                    placeholder="Add notes about your plant..."
                    multiline
                    numberOfLines={3}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Location</Text>
                  <TextInput
                    style={styles.textInput}
                    value={editedLocation}
                    onChangeText={setEditedLocation}
                    placeholder="Where is this plant located in your garden?"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Health Status</Text>
                  <View style={styles.statusButtons}>
                    {(['healthy', 'sick', 'recovering', 'critical'] as const).map((status) => (
                      <TouchableOpacity
                        key={status}
                        style={[
                          styles.statusButton,
                          editedHealthStatus === status && styles.statusButtonActive
                        ]}
                        onPress={() => setEditedHealthStatus(status)}
                      >
                        <Text style={[
                          styles.statusButtonText,
                          editedHealthStatus === status && styles.statusButtonTextActive
                        ]}>
                          {getStatusText(status)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.inputGroup}>
                  <View style={styles.visibilityRow}>
                    <View style={styles.visibilityLeft}>
                      {editedIsPublic ? (
                        <Eye size={20} color={Colors.primary} />
                      ) : (
                        <EyeOff size={20} color={Colors.primary} />
                      )}
                      <Text style={styles.inputLabel}>Plant Visibility</Text>
                    </View>
                    <Switch
                      value={editedIsPublic}
                      onValueChange={setEditedIsPublic}
                      trackColor={{ false: Colors.border, true: Colors.primary }}
                      thumbColor={Colors.background}
                    />
                  </View>
                  <Text style={styles.visibilityDescription}>
                    {editedIsPublic ? 'Visible to public community' : 'Hidden from public'}
                  </Text>
                </View>

                {/* Edit actions moved to bottom */}
              </View>
            ) : (
              <View style={styles.displayInfo}>
                {plant.nickname && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Nickname:</Text>
                    <Text style={styles.infoValue}>{plant.nickname}</Text>
                  </View>
                )}
                
                {plant.notes && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Notes:</Text>
                    <Text style={styles.infoValue}>{plant.notes}</Text>
                  </View>
                )}

                {(plant.location || plant.locationInGarden) && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Location:</Text>
                    <Text style={styles.infoValue}>
                      {plant.locationInGarden || plant.location}
                    </Text>
                  </View>
                )}

                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Health Status:</Text>
                  <Text style={[styles.infoValue, { color: getHealthStatusColor(plant.healthStatus || 'healthy') }]}>
                    {getStatusText(plant.healthStatus || 'healthy')}
                  </Text>
                </View>

                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Visibility:</Text>
                  <View style={styles.visibilityDisplay}>
                    {plant.isPublic ? (
                      <Eye size={16} color={Colors.primary} />
                    ) : (
                      <EyeOff size={16} color={Colors.textMuted} />
                    )}
                    <Text style={[styles.infoValue, { marginLeft: 8 }]}>
                      {plant.isPublic ? 'Public' : 'Private'}
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </Card>

          {/* Diagnosis Information */}
          {plant.diagnosis && (
            <Card style={styles.diagnosisCard}>
              <Text style={styles.sectionTitle}>Plant Diagnosis</Text>

              {plant.diagnosis.diagnosedProblem && (
                <View style={styles.diagnosisRow}>
                  <Text style={styles.diagnosisLabel}>Problem:</Text>
                  <Text style={styles.diagnosisValue}>{plant.diagnosis.diagnosedProblem}</Text>
                </View>
              )}

              {plant.diagnosis.severity && (
                <View style={styles.diagnosisRow}>
                  <Text style={styles.diagnosisLabel}>Severity:</Text>
                  <Text style={[styles.diagnosisValue, {
                    color: plant.diagnosis.severity === 'critical' ? Colors.error :
                           plant.diagnosis.severity === 'severe' ? '#FF6B35' :
                           plant.diagnosis.severity === 'moderate' ? '#FFA500' : Colors.text
                  }]}>
                    {plant.diagnosis.severity.charAt(0).toUpperCase() + plant.diagnosis.severity.slice(1)}
                  </Text>
                </View>
              )}

              {plant.diagnosis.immediateActions && plant.diagnosis.immediateActions.length > 0 && (
                <View style={styles.diagnosisSection}>
                  <Text style={styles.diagnosisLabel}>Immediate Actions:</Text>
                  {plant.diagnosis.immediateActions.map((action, index) => (
                    <Text key={index} style={styles.diagnosisListItem}>• {action}</Text>
                  ))}
                </View>
              )}

              {plant.diagnosis.longTermCare && plant.diagnosis.longTermCare.length > 0 && (
                <View style={styles.diagnosisSection}>
                  <Text style={styles.diagnosisLabel}>Long-term Care:</Text>
                  {plant.diagnosis.longTermCare.map((care, index) => (
                    <Text key={index} style={styles.diagnosisListItem}>• {care}</Text>
                  ))}
                </View>
              )}

              {plant.diagnosis.prognosis && (
                <View style={styles.diagnosisRow}>
                  <Text style={styles.diagnosisLabel}>Prognosis:</Text>
                  <Text style={styles.diagnosisValue}>{plant.diagnosis.prognosis}</Text>
                </View>
              )}

              <View style={styles.diagnosisRow}>
                <Text style={styles.diagnosisLabel}>Diagnosed:</Text>
                <Text style={styles.diagnosisValue}>
                  {plant.diagnosis.createdAt.toLocaleDateString()}
                </Text>
              </View>
            </Card>
          )}

          {/* Plant Care Information */}
          {plant.description && (
            <Card style={styles.careCard}>
              <Text style={styles.sectionTitle}>About This Plant</Text>
              <Text style={styles.description}>{plant.description}</Text>
            </Card>
          )}

          {/* Comprehensive Care Instructions */}
          {plant.careInstructions && (
            <Card style={styles.careCard}>
              <Text style={styles.sectionTitle}>Care Instructions</Text>

              {/* Basic Care Requirements */}
              <View style={styles.careGrid}>
                <View style={styles.careItem}>
                  <Text style={styles.careLabel}>Light:</Text>
                  <Text style={styles.careValue}>{plant.careInstructions?.light || 'Medium'}</Text>
                </View>
                <View style={styles.careItem}>
                  <Text style={styles.careLabel}>Water:</Text>
                  <Text style={styles.careValue}>{plant.careInstructions?.water || 'Medium'}</Text>
                </View>
                <View style={styles.careItem}>
                  <Text style={styles.careLabel}>Temperature:</Text>
                  <Text style={styles.careValue}>
                    {plant.careInstructions?.temperature?.min || 18}°-{plant.careInstructions?.temperature?.max || 25}°{plant.careInstructions?.temperature?.unit || 'C'}
                  </Text>
                </View>
                <View style={styles.careItem}>
                  <Text style={styles.careLabel}>Humidity:</Text>
                  <Text style={styles.careValue}>{plant.careInstructions?.humidity || 'Medium'}</Text>
                </View>
              </View>

              {/* Detailed Care Information */}
              {plant.careInstructions?.soil && (
                <View style={styles.detailedCareSection}>
                  <Text style={styles.careSubtitle}>Soil Requirements</Text>
                  <Text style={styles.careDescription}>{plant.careInstructions.soil}</Text>
                </View>
              )}

              {plant.careInstructions?.fertilizer && (
                <View style={styles.detailedCareSection}>
                  <Text style={styles.careSubtitle}>Fertilizer</Text>
                  <Text style={styles.careDescription}>{plant.careInstructions.fertilizer}</Text>
                </View>
              )}

              {/* Seasonal Care - placeholder for future enhancement */}
              <View style={styles.detailedCareSection}>
                <Text style={styles.careSubtitle}>Seasonal Care</Text>
                <Text style={styles.careDescription}>
                  Spring & Summer: Increase watering frequency and provide regular fertilization during the growing season.
                  Fall & Winter: Reduce watering and stop fertilizing as growth slows down.
                </Text>
              </View>

              {/* Growth Information - placeholder for future enhancement */}
              <View style={styles.detailedCareSection}>
                <Text style={styles.careSubtitle}>Growth Characteristics</Text>
                <Text style={styles.careDescription}>
                  This plant typically has moderate growth rate and can reach mature size with proper care.
                  Regular pruning may be needed to maintain shape and encourage healthy growth.
                </Text>
              </View>

              {/* Toxicity Warning */}
              {plant.careInstructions?.toxicity && plant.careInstructions.toxicity !== 'none' && (
                <View style={styles.toxicityContainer}>
                  <Text style={styles.toxicityLabel}>⚠️ Toxicity Warning</Text>
                  <Text style={styles.toxicityValue}>
                    {plant.careInstructions.toxicity.charAt(0).toUpperCase() + plant.careInstructions.toxicity.slice(1)} toxicity level.
                    Keep away from children and pets. Wash hands after handling.
                  </Text>
                </View>
              )}
            </Card>
          )}

          {/* Tags */}
          {plant.tags && plant.tags.length > 0 && (
            <Card style={styles.careCard}>
              <Text style={styles.sectionTitle}>Tags</Text>
              <View style={styles.tagsContainer}>
                {plant.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            </Card>
          )}

          {/* Bottom Actions */}
          <View style={styles.bottomActions}>
            {isEditing ? (
              <>
                <TouchableOpacity
                  onPress={() => setIsEditing(false)}
                  style={[styles.actionButton, styles.cancelActionButton]}
                  testID="cancel-edit-button"
                >
                  <XCircle size={24} color={Colors.textMuted} />
                  <Text style={[styles.actionButtonText, styles.cancelActionText]}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleSave}
                  style={[styles.actionButton, styles.saveActionButton]}
                  testID="save-edit-button"
                >
                  <Check size={24} color={Colors.background} />
                  <Text style={[styles.actionButtonText, styles.saveActionText]}>Save</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  onPress={() => setIsEditing(true)}
                  style={[styles.actionButton, styles.editActionButton]}
                  testID="edit-plant-button"
                >
                  <Edit3 size={24} color={Colors.primary} />
                  <Text style={[styles.actionButtonText, styles.editActionText]}>Edit</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleRemove}
                  style={[styles.actionButton, styles.deleteActionButton]}
                  testID="delete-plant-button"
                >
                  <Trash2 size={24} color={Colors.error} />
                  <Text style={[styles.actionButtonText, styles.deleteActionText]}>Delete</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={onClose}
                  style={[styles.actionButton, styles.closeActionButton]}
                  testID="close-plant-button"
                >
                  <X size={24} color={Colors.textMuted} />
                  <Text style={[styles.actionButtonText, styles.closeActionText]}>Cancel</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  plantCard: {
    marginBottom: 16,
  },
  plantImage: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    marginBottom: 16,
  },
  plantInfo: {
    paddingHorizontal: 4,
  },
  plantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  plantNames: {
    flex: 1,
  },
  commonName: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textMuted,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.background,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    fontSize: 14,
    color: Colors.textMuted,
  },
  detailsCard: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  editButton: {
    padding: 8,
  },
  editForm: {
    gap: 16,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
  },
  textInput: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  notesInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.cardBackground,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statusButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  statusButtonText: {
    fontSize: 14,
    color: Colors.text,
  },
  statusButtonTextActive: {
    color: Colors.background,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  visibilityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  visibilityLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  visibilityDescription: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
  visibilityDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  displayInfo: {
    gap: 12,
  },
  infoRow: {
    gap: 4,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textMuted,
  },
  infoValue: {
    fontSize: 16,
    color: Colors.text,
  },
  diagnosisCard: {
    marginBottom: 16,
    backgroundColor: '#FFF8E1',
    borderLeftWidth: 4,
    borderLeftColor: '#FFA500',
  },
  diagnosisRow: {
    marginBottom: 8,
  },
  diagnosisSection: {
    marginBottom: 12,
  },
  diagnosisLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  diagnosisValue: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  diagnosisListItem: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginLeft: 8,
    marginBottom: 2,
  },
  careCard: {
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  bottomActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 32,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    minWidth: 80,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  editActionButton: {
    backgroundColor: Colors.secondary,
  },
  editActionText: {
    color: Colors.primary,
  },
  deleteActionButton: {
    backgroundColor: '#FFEBEE',
  },
  deleteActionText: {
    color: Colors.error,
  },
  cancelActionButton: {
    backgroundColor: Colors.cardBackground,
  },
  cancelActionText: {
    color: Colors.textMuted,
  },
  saveActionButton: {
    backgroundColor: Colors.primary,
  },
  saveActionText: {
    color: Colors.background,
  },
  closeActionButton: {
    backgroundColor: Colors.cardBackground,
  },
  closeActionText: {
    color: Colors.textMuted,
  },
  // New comprehensive care display styles
  careGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '45%',
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginRight: 4,
  },
  careValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  soilContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  fertilizerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  detailedCareSection: {
    marginTop: 16,
    marginBottom: 8,
  },
  careSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  careDescription: {
    fontSize: 14,
    color: Colors.textMuted,
    lineHeight: 20,
  },
  toxicityContainer: {
    backgroundColor: Colors.errorLight,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  toxicityLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.error,
    marginBottom: 4,
  },
  toxicityValue: {
    fontSize: 14,
    color: Colors.error,
    lineHeight: 18,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
});
