import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ActivityIndicator } from 'react-native';
import { Colors } from '@/constants/colors';

interface ProgressIndicatorProps {
  mode: 'identify' | 'diagnose';
}

const IDENTIFICATION_MESSAGES = [
  { text: "Analyzing image...", duration: 2000 },
  { text: "Identifying plant species...", duration: 3000 },
  { text: "Gathering care information...", duration: 2500 },
  { text: "Almost there!", duration: 1500 },
];

const DIAGNOSIS_MESSAGES = [
  { text: "Analyzing plant health...", duration: 2000 },
  { text: "Identifying potential issues...", duration: 3000 },
  { text: "Preparing treatment recommendations...", duration: 2500 },
  { text: "Finalizing diagnosis...", duration: 1500 },
];

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ mode }) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [progress, setProgress] = useState(0);

  const messages = mode === 'identify' ? IDENTIFICATION_MESSAGES : DIAGNOSIS_MESSAGES;

  useEffect(() => {
    const totalDuration = messages.reduce((sum, msg) => sum + msg.duration, 0);
    let elapsed = 0;

    const interval = setInterval(() => {
      elapsed += 100;
      const newProgress = Math.min((elapsed / totalDuration) * 100, 95); // Cap at 95%
      setProgress(newProgress);

      // Update message based on elapsed time
      let cumulativeDuration = 0;
      for (let i = 0; i < messages.length; i++) {
        cumulativeDuration += messages[i].duration;
        if (elapsed <= cumulativeDuration) {
          setCurrentMessageIndex(i);
          break;
        }
      }
    }, 100);

    return () => clearInterval(interval);
  }, [messages]);

  return (
    <View style={styles.container} testID="progress-indicator">
      <View style={styles.content}>
        <ActivityIndicator size="large" color={Colors.primary} style={styles.spinner} />
        
        <Text style={styles.message}>{messages[currentMessageIndex]?.text}</Text>
        
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View 
              style={[
                styles.progressBarFill, 
                { width: `${progress}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>{Math.round(progress)}%</Text>
        </View>
        
        <Text style={styles.subtitle}>
          {mode === 'identify' 
            ? "Our AI is analyzing your plant image..." 
            : "Diagnosing your plant's health condition..."
          }
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  spinner: {
    marginBottom: 24,
  },
  message: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  progressBarContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressBarBackground: {
    width: '100%',
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textMuted,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },
});
