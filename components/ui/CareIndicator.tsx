import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Droplets, Sun, Thermometer } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { LightRequirement, WaterRequirement, HumidityLevel } from '@/types/plant';

interface CareIndicatorProps {
  type: 'light' | 'water' | 'humidity';
  level: LightRequirement | WaterRequirement | HumidityLevel;
  showLabel?: boolean;
}

export const CareIndicator: React.FC<CareIndicatorProps> = ({
  type,
  level,
  showLabel = true,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'light':
        return <Sun size={20} color={Colors.primaryDark} />;
      case 'water':
        return <Droplets size={20} color={Colors.primaryDark} />;
      case 'humidity':
        return <Thermometer size={20} color={Colors.primaryDark} />;
    }
  };

  const getLabel = () => {
    return `${level.charAt(0).toUpperCase() + level.slice(1)} ${type}`;
  };

  const getFilledDots = () => {
    switch (level) {
      case 'low':
        return 1;
      case 'medium':
        return 2;
      case 'high':
        return 3;
      default:
        return 0;
    }
  };

  const filledDots = getFilledDots();

  return (
    <View style={styles.container} testID={`care-indicator-${type}`}>
      <View style={styles.iconContainer}>{getIcon()}</View>
      <View style={styles.content}>
        {showLabel && <Text style={styles.label}>{getLabel()}</Text>}
        <View style={styles.dots}>
          {[1, 2, 3].map((dot) => (
            <View
              key={dot}
              style={[
                styles.dot,
                dot <= filledDots ? styles.filledDot : styles.emptyDot,
              ]}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    color: Colors.text,
    marginBottom: 4,
  },
  dots: {
    flexDirection: 'row',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  filledDot: {
    backgroundColor: Colors.primary,
  },
  emptyDot: {
    backgroundColor: Colors.border,
  },
});