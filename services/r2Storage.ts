import Constants from 'expo-constants';

// R2 Configuration from environment variables
const R2_CONFIG = {
  ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
  ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
  BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
  ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
};

// Validate R2 configuration
const validateR2Config = (): boolean => {
  const required = ['ACCOUNT_ID', 'ACCESS_KEY_ID', 'SECRET_ACCESS_KEY', 'BUCKET_NAME', 'ENDPOINT_URL'];
  const missing = required.filter(key => !R2_CONFIG[key as keyof typeof R2_CONFIG]);
  
  if (missing.length > 0) {
    console.warn('Missing R2 configuration:', missing);
    return false;
  }
  
  return true;
};

// Simplified R2 Client using presigned URLs (requires backend support)
// For now, we'll implement a direct upload approach
class R2Client {
  private config = R2_CONFIG;

  async uploadFile(
    key: string,
    file: Blob,
    contentType: string = 'application/octet-stream'
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      // For now, we'll use a simple approach that requires CORS to be configured on R2
      // In production, you should use presigned URLs or a backend service
      console.log('R2 direct upload not implemented yet - falling back to Supabase');

      return {
        success: false,
        error: 'R2 direct upload requires backend implementation for security',
      };
    } catch (error) {
      console.error('R2 upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('R2 delete not implemented yet - falling back to Supabase');

      return {
        success: false,
        error: 'R2 delete requires backend implementation for security',
      };
    } catch (error) {
      console.error('R2 delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }
}

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Main storage service that can fallback between R2 and Supabase
export class StorageService {
  private static r2Client = new R2Client();

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    // Try R2 first if configured
    if (validateR2Config()) {
      console.log('Uploading to Cloudflare R2...');
      const result = await this.r2Client.uploadFile(path, file, contentType);
      if (result.success) {
        return result;
      }
      console.warn('R2 upload failed, falling back to Supabase:', result.error);
    }

    // Fallback to Supabase storage
    console.log('Uploading to Supabase storage...');
    return this.uploadToSupabase(file, path, contentType);
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      const { supabase } = await import('@/lib/supabase');
      
      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('Supabase upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    // Try R2 first if configured
    if (validateR2Config()) {
      const result = await this.r2Client.deleteFile(path);
      if (result.success) {
        return result;
      }
    }

    // Fallback to Supabase storage
    try {
      const { supabase } = await import('@/lib/supabase');
      
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${type}s/${userId}-${timestamp}-${random}.${extension}`;
  }
}
