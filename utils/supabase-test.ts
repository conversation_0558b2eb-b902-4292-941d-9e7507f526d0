/**
 * Utility functions to test Supabase configuration and connectivity
 */

import { supabase } from '@/lib/supabase';

/**
 * Test Supabase configuration and basic connectivity
 */
export const testSupabaseConnection = async (): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> => {
  try {
    console.log('Testing Supabase connection...');
    
    // Test 1: Check if client is initialized
    if (!supabase) {
      return {
        success: false,
        error: 'Supabase client not initialized'
      };
    }

    // Test 2: Test basic auth functionality
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session test failed:', sessionError);
      return {
        success: false,
        error: `Session test failed: ${sessionError.message}`,
        details: sessionError
      };
    }

    // Test 3: Test basic database connectivity (try to query a system table)
    const { data: healthData, error: healthError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(0);

    if (healthError) {
      console.error('Database connectivity test failed:', healthError);
      return {
        success: false,
        error: `Database test failed: ${healthError.message}`,
        details: healthError
      };
    }

    console.log('Supabase connection test passed');
    return {
      success: true,
      details: {
        hasSession: !!sessionData.session,
        userId: sessionData.session?.user?.id,
        databaseConnected: true
      }
    };

  } catch (error) {
    console.error('Supabase connection test error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during connection test',
      details: error
    };
  }
};

/**
 * Test user profile operations
 */
export const testUserProfileOperations = async (userId: string): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> => {
  try {
    console.log('Testing user profile operations for user:', userId);

    // Test reading user profile
    const { data: profile, error: readError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (readError && readError.code !== 'PGRST116') {
      // PGRST116 is "not found" which is acceptable
      return {
        success: false,
        error: `Profile read test failed: ${readError.message}`,
        details: readError
      };
    }

    console.log('User profile operations test passed');
    return {
      success: true,
      details: {
        profileExists: !!profile,
        profileData: profile
      }
    };

  } catch (error) {
    console.error('User profile operations test error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during profile operations test',
      details: error
    };
  }
};

/**
 * Run comprehensive Supabase diagnostics
 */
export const runSupabaseDiagnostics = async (userId?: string): Promise<{
  connectionTest: any;
  profileTest?: any;
  recommendations: string[];
}> => {
  const results = {
    connectionTest: await testSupabaseConnection(),
    profileTest: undefined as any,
    recommendations: [] as string[]
  };

  // Add recommendations based on connection test
  if (!results.connectionTest.success) {
    if (results.connectionTest.error?.includes('Missing Supabase environment variables')) {
      results.recommendations.push('Check that .env.local file exists and contains EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_KEY');
    } else if (results.connectionTest.error?.includes('network') || results.connectionTest.error?.includes('fetch')) {
      results.recommendations.push('Check internet connection and Supabase service status');
    } else if (results.connectionTest.error?.includes('JWT') || results.connectionTest.error?.includes('auth')) {
      results.recommendations.push('Check Supabase authentication configuration and user session');
    } else {
      results.recommendations.push('Check Supabase configuration and credentials');
    }
  }

  // Test profile operations if user ID provided and connection test passed
  if (userId && results.connectionTest.success) {
    results.profileTest = await testUserProfileOperations(userId);
    
    if (!results.profileTest.success) {
      if (results.profileTest.error?.includes('JWT')) {
        results.recommendations.push('User session may be expired - try logging out and back in');
      } else if (results.profileTest.error?.includes('permission')) {
        results.recommendations.push('Check Row Level Security policies for user_profiles table');
      } else {
        results.recommendations.push('Check user_profiles table structure and permissions');
      }
    }
  }

  return results;
};
