import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_KEY;

console.log('Supabase config:', {
  url: supabaseUrl ? 'loaded' : 'missing',
  key: supabaseAnonKey ? 'loaded' : 'missing',
  source: Constants.expoConfig?.extra?.supabaseUrl ? 'expo-config' : 'env-vars',
  urlPreview: supabaseUrl ? `${supabaseUrl.substring(0, 20)}...` : 'N/A',
  keyPreview: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'N/A'
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase credentials missing:', {
    supabaseUrl: supabaseUrl || 'MISSING',
    supabaseAnonKey: supabaseAnonKey || 'MISSING'
  });
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch (error) {
  console.error('Invalid Supabase URL format:', supabaseUrl);
  throw new Error('Invalid Supabase URL format. Please check your configuration.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true, // Enable for OAuth redirects on web
    flowType: 'pkce', // Use PKCE flow for better security
  },
});

// Test connection on initialization
supabase.auth.getSession().then(({ data, error }) => {
  if (error) {
    console.error('Supabase connection test failed:', error);
  } else {
    console.log('Supabase client initialized successfully');
  }
}).catch((error) => {
  console.error('Supabase initialization error:', error);
});
